-- name: CreateCompany :one
INSERT INTO companies (
  name, cnpj, bio, picture, phone_numbers, pix_key, delivery_modes, shipping_fee, commission_rate, cashback_rate, external_id, is_active, owner_id
) VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING id;

-- name: UpdateCompanyData :exec
UPDATE companies
SET bio = $1,
  phone_numbers = $2,
  delivery_modes = $3,
  shipping_fee = $4
WHERE external_id = $5;

-- name: UpdateCompanyComissionRate :exec
UPDATE companies
SET commission_rate = $1
WHERE external_id = $2;

-- name: UpdateCompanyCashbackRate :exec
UPDATE companies
SET cashback_rate = $1
WHERE external_id = $2;

-- name: CreateCompanyAddress :exec
INSERT INTO company_addresses (
  company_id, name, street, number, complement, neighborhood, city, state, zip_code,
  latitude, longitude, location, is_default, external_id
) VALUES (
  sqlc.arg(company_id), sqlc.arg(name), sqlc.arg(street), sqlc.arg(number),
  sqlc.arg(complement), sqlc.arg(neighborhood), sqlc.arg(city), sqlc.arg(state),
  sqlc.arg(zip_code), sqlc.arg(latitude), sqlc.arg(longitude),
  ST_SetSRID(ST_MakePoint(sqlc.arg(longitude), sqlc.arg(latitude)), 4326),
  sqlc.arg(is_default), sqlc.arg(external_id)
);

-- name: UpdateCompanyAddress :exec
UPDATE company_addresses
SET name = $1,
  street = $2,
  number = $3,
  complement = $4,
  neighborhood = $5,
  city = $6,
  state = $7,
  zip_code = $8,
  latitude = $9,
  longitude = $10,
  location = ST_SetSRID(ST_MakePoint($10, $9), 4326)
WHERE external_id = $11;


-- name: GetActiveCompanies :many
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,  
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'name', p.name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
      'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      )
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products,
  COUNT(*) OVER() AS total_count
FROM companies c
LEFT JOIN company_addresses ca ON c.id = ca.company_id
LEFT JOIN company_products cp ON c.id = cp.company_id
LEFT JOIN products p ON cp.product_id = p.id
WHERE c.is_active = true
GROUP BY c.id
LIMIT $1 OFFSET $2;

-- name: GetActiveCompaniesWithLocation :many
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', p.external_id,
      'name', p.name,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'ean', p.ean,
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'is_18_plus', p.is_18_plus,
      'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      ),
      'is_active', p.is_active,
      'is_reviewed', p.is_reviewed,
      'created_at', p.created_at,
      'updated_at', p.updated_at
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products,
  COUNT(*) OVER() AS total_count,
  ROUND(
    (ST_Distance(ca.location::geography, ST_MakePoint($3::float8, $4::float8)::geography) / 1000.0)::numeric, 2
  ) AS distance_km
FROM companies c
LEFT JOIN company_addresses ca ON c.id = ca.company_id AND ca.is_default = true
LEFT JOIN company_products cp ON c.id = cp.company_id
LEFT JOIN products p ON cp.product_id = p.id
WHERE c.is_active = true
  AND ca.location IS NOT NULL
  AND ST_DWithin(ca.location, ST_MakePoint($3::float8, $4::float8)::geography, $5::int4)
GROUP BY c.id, ca.location
ORDER BY distance_km ASC
LIMIT $1 OFFSET $2;

-- name: GetAllCompanies :many
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'name', p.name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
      'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      )
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products,
  COUNT(*) OVER() AS total_count
FROM companies c
LEFT JOIN company_addresses ca ON c.id = ca.company_id
LEFT JOIN company_products cp ON c.id = cp.company_id
LEFT JOIN products p ON cp.product_id = p.id
GROUP BY c.id
LIMIT $1 OFFSET $2;


-- name: GetCompanyByID :one
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at
FROM
  public.companies c
WHERE
  c.id = $1;

-- name: GetCompanyByExternalID :one
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.created_at,
  c.updated_at,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'name', p.name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
       'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      )
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products
FROM companies c
LEFT JOIN company_addresses ca ON c.id = ca.company_id
LEFT JOIN company_products cp ON c.id = cp.company_id
LEFT JOIN products p ON cp.product_id = p.id
WHERE c.external_id = $1
GROUP BY c.id;


-- name: AddProductsToCompany :exec
WITH company AS (
  SELECT id AS company_id
  FROM companies
  WHERE companies.external_id = sqlc.arg(company_external_id)
),
product AS (
  SELECT id AS product_id
  FROM products
  WHERE products.external_id = sqlc.arg(product_external_id)
)
INSERT INTO company_products (
  company_id, product_id, price, discount, stock
)
SELECT
  c.company_id,
  p.product_id,
  sqlc.arg(price)::int,
  sqlc.arg(discount)::int,
  sqlc.arg(stock)::int
FROM company c, product p
ON CONFLICT (company_id, product_id) DO UPDATE
SET 
  price = EXCLUDED.price,
  discount = EXCLUDED.discount,
  stock = EXCLUDED.stock,
  created_at = now();

-- name: RemoveProductsFromCompany :exec
WITH company AS (
  SELECT id AS company_id
  FROM companies
  WHERE companies.external_id = sqlc.arg(company_external_id)
),
product AS (
  SELECT id AS product_id
  FROM products
  WHERE products.external_id = sqlc.arg(product_external_id)
)
DELETE FROM company_products
WHERE company_id = (SELECT company_id FROM company)
  AND product_id = (SELECT product_id FROM product);

-- name: ActivateCompany :one
UPDATE companies
SET is_active = true, updated_at = now()
WHERE external_id = $1
RETURNING id, name, external_id, is_active, owner_id;

-- name: UpdateCompanyStatus :one
UPDATE companies
SET is_active = $2, updated_at = now()
WHERE external_id = $1
RETURNING id, name, external_id, is_active, owner_id;

-- name: UpdateCompanyOwner :one
UPDATE companies
SET owner_id = $2, updated_at = now()
WHERE external_id = $1
RETURNING id, name, external_id, owner_id;


-- name: UpdateCompanyImage :exec
UPDATE companies
SET picture = $1
WHERE external_id = $2;

-- name: GetCompaniesByOwnerID :many
SELECT external_id
FROM companies
WHERE owner_id = $1 AND is_active = true;

-- name: CountCompaniesByOwnerID :one
SELECT COUNT(*) AS company_count
FROM companies
WHERE owner_id = $1;

-- name: GetCompanyDetailsWithOwner :one
SELECT
  c.id,
  c.name,
  c.cnpj,
  c.bio,
  c.picture,
  c.phone_numbers,
  c.pix_key,
  c.subscription_id,
  c.rating,
  c.shipping_fee,
  c.owner_id,
  c.affiliate_balance,
  c.delivery_modes,
  c.external_id,
  c.is_active,
  c.commission_rate,
  c.cashback_rate,
  c.created_at,
  c.updated_at,
  u.external_id AS owner_external_id,
  u.name AS owner_name,
  u.email AS owner_email,
  json_agg(
    DISTINCT jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', ca.latitude,
        'longitude', ca.longitude
      )
    )
  ) FILTER (WHERE ca.id IS NOT NULL) AS addresses,
  json_agg(
    DISTINCT jsonb_build_object(
      'price', cp.price,
      'discount', cp.discount,
      'stock', cp.stock,
      'name', p.name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
      'categories', (
        SELECT json_agg(DISTINCT jsonb_build_object(
          'name', cat.name,
          'image', cat.image,
          'external_id', cat.external_id
        ))
        FROM products_categories pc
        JOIN categories cat ON cat.id = pc.category_id
        WHERE pc.product_id = p.id
      )
    )
  ) FILTER (WHERE cp.product_id IS NOT NULL) AS products
FROM companies c
LEFT JOIN users u ON c.owner_id = u.id
LEFT JOIN company_addresses ca ON c.id = ca.company_id
LEFT JOIN company_products cp ON c.id = cp.company_id
LEFT JOIN products p ON cp.product_id = p.id
WHERE c.external_id = $1
GROUP BY c.id, u.external_id, u.name, u.email;

-- name: GetCompanyProductsByCategory :one
WITH company_info AS (
  SELECT
    c.id AS company_id,
    c.name AS company_name,
    c.external_id AS company_external_id
  FROM companies c
  WHERE c.external_id = sqlc.arg(company_external_id)
    AND c.is_active = true
),
base_products AS (
  SELECT
    p.id,
    p.name AS product_name,
    p.ean,
    p.description,
    p.image,
    p.brand,
    p.is_reviewed,
    p.is_active,
    p.is_18_plus,
    p.external_id,
    cp.price,
    cp.discount,
    cp.stock,
    cat.name AS category_name,
    cat.image AS category_image,
    cat.external_id AS category_external_id
  FROM company_products cp
  JOIN products p ON p.id = cp.product_id
  JOIN products_categories pc ON pc.product_id = p.id
  JOIN categories cat ON cat.id = pc.category_id
  JOIN company_info ci ON ci.company_id = cp.company_id
  WHERE cat.external_id = sqlc.arg(category_external_id)
    AND p.is_reviewed = true
    AND p.is_active = true
),
total AS (
  SELECT COUNT(*) AS total_count FROM base_products
),
paginated_products AS (
  SELECT * FROM base_products
  ORDER BY product_name
  LIMIT $1 OFFSET $2
)
SELECT
  (SELECT total_count FROM total),
  (SELECT category_name FROM base_products LIMIT 1),
  (SELECT category_image FROM base_products LIMIT 1),
  (SELECT category_external_id FROM base_products LIMIT 1),
  (SELECT company_name FROM company_info),
  (SELECT company_external_id FROM company_info),
  json_agg(
    jsonb_build_object(
      'name', p.product_name,
      'ean', p.ean,
      'description', p.description,
      'image', p.image,
      'brand', p.brand,
      'categories', jsonb_build_array(
        jsonb_build_object(
          'name', p.category_name,
          'image', p.category_image,
          'external_id', p.category_external_id
        )
      ),
      'is_reviewed', p.is_reviewed,
      'is_active', p.is_active,
      'is_18_plus', p.is_18_plus,
      'external_id', p.external_id,
      'price', p.price,
      'discount', p.discount,
      'stock', p.stock
    )
  ) AS products
FROM paginated_products p;

-- name: CreateWithdrawal :one
INSERT INTO company_withdrawals (
  company_id,
  amount,
  correlation_id,
  destination_pix_key,
  comment,
  end_to_end_id,
  woovi_response
) VALUES (
  $1, $2, $3, $4, $5, $6, $7
) RETURNING id, created_at;

-- name: GetWithdrawalsByCompanyAndPeriod :many
WITH company_info AS (
  SELECT id, external_id, name
  FROM companies
  WHERE external_id = $1
),
period_filter AS (
  SELECT
    CASE
      WHEN $4::text = '1m' THEN NOW() - INTERVAL '1 month'
      WHEN $4::text = '3m' THEN NOW() - INTERVAL '3 months'
      WHEN $4::text = '6m' THEN NOW() - INTERVAL '6 months'
      WHEN $4::text = '1y' THEN NOW() - INTERVAL '1 year'
      ELSE NOW() - INTERVAL '30 days' -- Default to 30 days
    END AS start_date
),
filtered_withdrawals AS (
  SELECT
    cw.id,
    cw.amount,
    cw.status,
    cw.correlation_id,
    cw.destination_pix_key,
    cw.comment,
    cw.end_to_end_id,
    cw.woovi_response,
    cw.created_at,
    ci.external_id as company_external_id,
    ci.name as company_name
  FROM company_withdrawals cw
  JOIN company_info ci ON cw.company_id = ci.id
  CROSS JOIN period_filter pf
  WHERE cw.created_at >= pf.start_date
  ORDER BY cw.created_at DESC
),
total_info AS (
  SELECT
    COUNT(*) as total_count,
    COALESCE(SUM(amount), 0) as total_amount
  FROM filtered_withdrawals
)
SELECT
  fw.id,
  fw.amount,
  fw.status,
  fw.correlation_id,
  fw.destination_pix_key,
  fw.comment,
  fw.end_to_end_id,
  fw.woovi_response,
  fw.created_at,
  fw.company_external_id,
  fw.company_name,
  ti.total_count,
  ti.total_amount
FROM filtered_withdrawals fw
CROSS JOIN total_info ti
LIMIT $2 OFFSET $3;

-- name: UpdateWithdrawalStatusByCorrelationID :execrows
UPDATE company_withdrawals
SET status = $1, finished_at = NOW()
WHERE correlation_id = $2;

-- name: ValidateWithdrawalEligibility :one
WITH company_info AS (
  SELECT id, external_id, name
  FROM companies
  WHERE external_id = $1
)
SELECT
  ci.id as company_id,
  ci.external_id,
  ci.name,
  COALESCE(SUM(ip.amount), 0) as available_balance,
  CASE
    WHEN COALESCE(SUM(ip.amount), 0) >= $2 THEN true
    ELSE false
  END as is_eligible,
  $2 as minimum_threshold
FROM company_info ci
LEFT JOIN invoice_payouts ip ON ip.company_id = ci.id
  AND ip.status = 'available'
  AND ip.available_after <= NOW()
GROUP BY ci.id, ci.external_id, ci.name;

-- name: GetWithdrawalHistoryWithPayouts :many
WITH company_info AS (
  SELECT id, external_id, name
  FROM companies
  WHERE external_id = $1
),
period_filter AS (
  SELECT
    CASE
      WHEN $4::text = '1m' THEN NOW() - INTERVAL '1 month'
      WHEN $4::text = '3m' THEN NOW() - INTERVAL '3 months'
      WHEN $4::text = '6m' THEN NOW() - INTERVAL '6 months'
      WHEN $4::text = '1y' THEN NOW() - INTERVAL '1 year'
      ELSE NOW() - INTERVAL '30 days' -- Default to 30 days
    END AS start_date
),
filtered_withdrawals AS (
  SELECT
    cw.id,
    cw.amount,
    cw.status,
    cw.correlation_id,
    cw.destination_pix_key,
    cw.comment,
    cw.end_to_end_id,
    cw.woovi_response,
    cw.finished_at,
    cw.created_at,
    ci.external_id as company_external_id,
    ci.name as company_name,
    json_agg(
      DISTINCT jsonb_build_object(
        'payout_id', ip.id,
        'invoice_id', ip.invoice_id,
        'order_id', i.order_id,
        'amount', ip.amount,
        'finalized_at', ip.finalized_at
      )
    ) FILTER (WHERE ip.id IS NOT NULL) as used_payouts
  FROM company_withdrawals cw
  JOIN company_info ci ON cw.company_id = ci.id
  LEFT JOIN withdrawal_payouts wp ON wp.withdrawal_id = cw.id
  LEFT JOIN invoice_payouts ip ON ip.id = wp.payout_id
  LEFT JOIN invoices i ON i.id = ip.invoice_id
  CROSS JOIN period_filter pf
  WHERE cw.created_at >= pf.start_date
  GROUP BY cw.id, ci.external_id, ci.name
  ORDER BY cw.created_at DESC
),
total_info AS (
  SELECT
    COUNT(*) as total_count,
    COALESCE(SUM(amount), 0) as total_amount
  FROM filtered_withdrawals
)
SELECT
  fw.id,
  fw.amount,
  fw.status,
  fw.correlation_id,
  fw.destination_pix_key,
  fw.comment,
  fw.end_to_end_id,
  fw.woovi_response,
  fw.finished_at,
  fw.created_at,
  fw.company_external_id,
  fw.company_name,
  fw.used_payouts,
  ti.total_count,
  ti.total_amount
FROM filtered_withdrawals fw
CROSS JOIN total_info ti
LIMIT $2 OFFSET $3;

-- name: SearchCompanies :many
SELECT
  c.external_id,
  c.name,
  c.cnpj,
  c.phone_numbers,
  c.created_at,
  c.updated_at,
  COUNT(*) OVER() AS total_count
FROM companies c
WHERE (
  c.cnpj ILIKE '%' || sqlc.arg(search_query)::text || '%' OR
  c.name ILIKE '%' || sqlc.arg(search_query)::text || '%'
) AND c.is_active = true
ORDER BY c.created_at DESC
LIMIT $1 OFFSET $2;
package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"log"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/fcm"
	"github.com/izy-mercado/backend/internal/helpers"
	"github.com/izy-mercado/backend/internal/http/common"
	mailer "github.com/izy-mercado/backend/internal/integrations/mailer"
	"github.com/izy-mercado/backend/internal/integrations/payment/woovi"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
	"github.com/jackc/pgtype"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/oklog/ulid/v2"
)

type UserHandler struct {
	queries    *postgres.Queries
	pool       *pgxpool.Pool
	env        *config.Environment
	fcmService *fcm.Service
	woovi      *woovi.Woovi
}

type CreateUserRequest struct {
	Name         string   `json:"name" example:"Vini" validate:"required"`
	Email        string   `json:"email" example:"<EMAIL>" validate:"required,email"`
	Cpf          string   `json:"cpf" example:"79220436191" validate:"required"`
	PhoneNumbers []string `json:"phone_numbers" example:"84999999999" validate:"required,dive,e164"`
}

type CheckIfEmailExistsRequest struct {
	Email string `json:"email" example:"<EMAIL>" validate:"required,email"`
}

type CreateProductsListRequest struct {
	IconURL  string                      `json:"icon_url" example:"https://images.izymercado.com.br/014KG56DC01GG4TEB01ZEX7WFJ.png" validate:"required"`
	Name     string                      `json:"name" example:"Compras do mês" validate:"required"`
	Products []custom_models.ProductList `json:"products"`
	IsPublic *bool                       `json:"is_public" example:"false" validate:"required"`
}

type GetMeResponse struct {
	Name           string                        `json:"name" example:"Vini"`
	Email          string                        `json:"email" example:"<EMAIL>"`
	Cpf            string                        `json:"cpf" example:"79220436191"`
	Addresses      []custom_models.AddressParams `json:"addresses"`
	PhoneNumbers   []string                      `json:"phone_numbers" example:"11999999999"`
	CashbackValue  int32                         `json:"cashback_value" example:"0"`
	SubscriptionID string                        `json:"subscription_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZ"`
	ExternalID     string                        `json:"external_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZ"`
}

type GetProductsListResponse struct {
	Name       string                  `json:"name" example:"Compras do mês"`
	IconURL    string                  `json:"icon_url" example:"https://images.izymercado.com.br/014KG56DC01GG4TEB01ZEX7WFJ.png"`
	Products   []custom_models.Product `json:"products"`
	IsPublic   bool                    `json:"is_public" example:"false"`
	ExternalID string                  `json:"external_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZQ"`
	CreatedAt  string                  `json:"created_at" example:"2021-09-01T00:00:00Z"`
	UpdatedAt  string                  `json:"updated_at" example:"2021-09-01T00:00:00Z"`
}

type CreateUserResponse struct {
	ExternalID string `json:"external_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZQ"`
}

type CreateProductsListResponse struct {
	ExternalID string `json:"external_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZQ"`
}

type CreateUserAddressResponse struct {
	ExternalID string `json:"external_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZQ"`
}

type UpdateUserNameRequest struct {
	Name string `json:"name" example:"Vini" validate:"required"`
}

type AssignRoleToUserRequest struct {
	ExternalID string `json:"external_id" example:"01F9ZQZQZQZQZQZQZQZQZQZQZQ" validate:"required"`
	Role       string `json:"role" example:"admin" validate:"required,oneof=admin partner user"`
}

type UpdateUserStatusRequest struct {
	IsActive *bool `json:"is_active" example:"true" validate:"required"`
}

type GetAllUsersResponse struct {
	Name       string   `json:"name"`
	Email      string   `json:"email"`
	ExternalID string   `json:"external_id"`
	Roles      []string `json:"roles"`
}

type SearchUsersResponse struct {
	ExternalID     string   `json:"external_id"`
	Name           string   `json:"name"`
	Email          string   `json:"email"`
	Cpf            string   `json:"cpf"`
	PhoneNumbers   []string `json:"phone_numbers"`
	CashbackValue  int32    `json:"cashback_value"`
	SubscriptionID *int32   `json:"subscription_id"`
	IsActive       bool     `json:"is_active"`
	IsDeleted      bool     `json:"is_deleted"`
	CreatedAt      string   `json:"created_at"`
	UpdatedAt      string   `json:"updated_at"`
}

type UpdateUserStatusResponse struct {
	ExternalID string `json:"external_id"`
	Name       string `json:"name"`
	IsActive   bool   `json:"is_active"`
}

type GetAllUsersSuccessResponse = common.SuccessResponseWithPagination[GetAllUsersResponse]
type SearchUsersSuccessResponse = common.SuccessResponseWithPagination[SearchUsersResponse]

type AssignRoleToUserSuccessResponse = common.SuccessResponse[interface{}]
type UpdateUserStatusSuccessResponse = common.SuccessResponse[UpdateUserStatusResponse]
type CreateUserSuccessResponse = common.SuccessResponse[CreateUserResponse]
type CreateProductsListSuccessResponse = common.SuccessResponse[CreateProductsListResponse]
type CheckIfEmailExistsSuccessResponse = common.SuccessResponse[bool]
type GetMeSuccessResponse = common.SuccessResponse[GetMeResponse]
type GetProductsListSuccessResponse = common.SuccessResponseWithPagination[GetProductsListResponse]
type CreateUserAddressSuccessResponse = common.SuccessResponse[CreateUserAddressResponse]

func NewUserHandler(env *config.Environment, queries *postgres.Queries, pool *pgxpool.Pool, fcmService *fcm.Service) *chi.Mux {
	router := chi.NewRouter()
	h := &UserHandler{
		queries:    queries,
		pool:       pool,
		env:        env,
		fcmService: fcmService,
		woovi: woovi.New(woovi.WooviConfig{
			APIKey: env.Woovi.API_KEY,
			Url:    env.Woovi.URL,
			PixKey: env.Woovi.PIX_KEY,
		}),
	}
	m := middlewares.New(env, queries)

	router.Post("/", h.Create)

	defaultRouter := router.With(m.DefaultPermissions)
	defaultRouter.Post("/address", h.AddAddress)
	defaultRouter.Delete("/address/{address_external_id}", h.DeleteAddress)
	defaultRouter.Get("/me", h.GetMe)
	defaultRouter.Delete("/me", h.Delete)
	defaultRouter.Post("/list", h.CreateList)
	defaultRouter.Delete("/list/{list_external_id}", h.DeleteList)
	defaultRouter.Get("/list", h.GetList)
	defaultRouter.Get("/list/{list_external_id}", h.GetOneList)
	router.Get("/list/icons", h.GetListIcons)
	defaultRouter.Patch("/name", h.UpdateName)
	defaultRouter.Get("/invoice", h.GetInvoices)
	defaultRouter.Get("/invoice/{order_id}", h.GetInvoiceByOrderID)
	defaultRouter.Put("/invoice/{order_id}/status", h.UpdateInvoiceStatus)
	router.Get("/list/template", h.GetTemplateList)
	router.Post("/exists/email", h.CheckIfEmailExists)

	adminRouter := router.With(m.AdminPermissions)
	adminRouter.Post("/assign-role", h.AssignRoleToUser)
	adminRouter.Patch("/{external_id}/status", h.UpdateUserStatus)
	adminRouter.Get("/search/{query}", h.SearchUser)

	return router
}

// Create godoc
// @Summary Create a new user
// @Description Create a new user
// @Tags Users
// @Accept json
// @Produce json
// @Param payload body CreateUserRequest true "User payload"
// @Success 201 {object} CreateUserSuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user [post]
func (h *UserHandler) Create(w http.ResponseWriter, r *http.Request) {
	payload := CreateUserRequest{}
	helper := helpers.New()

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	if !helper.IsValidCPF(payload.Cpf) {
		common.RespondError(w, fmt.Errorf("invalid cpf"), 400)
		return
	}

	randomCode := helper.GenerateRandomFiveDigitsCode()

	//start transaction
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	user, err := qtx.CreateUser(r.Context(), postgres.CreateUserParams{
		Name:         payload.Name,
		Email:        payload.Email,
		LoginCode:    randomCode,
		Cpf:          payload.Cpf,
		ExternalID:   ulid.Make().String(),
		PhoneNumbers: payload.PhoneNumbers,
	})
	if err != nil {
		common.RespondError(w, err)
		return
	}

	err = qtx.AssignRoleToUser(r.Context(), postgres.AssignRoleToUserParams{
		UserID: user.ID,
		Name:   "user",
	})
	if err != nil {
		log.Println("Failed to assign role to user: ", err)
		common.RespondError(w, err)
		return
	}

	err = tx.Commit(r.Context())
	if err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}

	go mailer.New(mailer.MailerConfig{
		MailerURL:   h.env.Mailer.URL,
		MailerToken: h.env.Mailer.TOKEN,
		From:        h.env.Mailer.FROM,
		To:          payload.Email,
		Code:        randomCode,
		UserName:    payload.Name,
		TemplateKey: h.env.Mailer.SEND_CODE_TEMPLATE_KEY,
	}).Send()

	common.RespondSuccess(w, user.ExternalID, http.StatusCreated)
}

// Delete godoc
// @Summary Delete user
// @Description Delete user
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} EmptySuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 401 {object} common.ErrorResponse "unauthorized"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/me [delete]
func (h *UserHandler) Delete(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	err = qtx.SoftDeleteUser(r.Context(), user.ID)
	if err != nil {
		log.Println("Error deleting user: ", err)
		common.RespondError(w, err)
		return
	}

	err = qtx.InvalidateSession(r.Context(), user.SessionID)
	if err != nil {
		log.Println("Failed to invalidate session: ", err)
		common.RespondError(w, err)
		return
	}

	err = tx.Commit(r.Context())
	if err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// CheckIfEmailExists godoc
// @Summary Check if email exists
// @Description Check if email exists
// @Tags Users
// @Accept json
// @Produce json
// @Param payload body CheckIfEmailExistsRequest true "User payload"
// @Success 200 {object} CheckIfEmailExistsSuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/exists/email [post]
func (h *UserHandler) CheckIfEmailExists(w http.ResponseWriter, r *http.Request) {
	payload := CheckIfEmailExistsRequest{}
	helper := helpers.New()

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	_, err = h.queries.GetUserByEmail(r.Context(), payload.Email)
	if err != nil {
		log.Println("Error getting user by email: ", err)
		//TODO: mudar para  errors.Is(err, sql.ErrNoRows)
		if err.Error() != "no rows in result set" {
			common.RespondError(w, err)
			return
		}
		common.RespondSuccess(w, false, http.StatusOK)
		return
	}

	common.RespondSuccess(w, true, http.StatusOK)
}

// GetTemplateList godoc
// @Summary Get random template products list
// @Description Get random template products list
// @Tags Users
// @Accept json
// @Produce json
// @Success 200 {object} GetProductsListSuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/list/template [get]
func (h *UserHandler) GetTemplateList(w http.ResponseWriter, r *http.Request) {
	template, err := h.queries.GetRandomTemplateList(r.Context())
	if err != nil {
		if err.Error() == "no rows in result set" {
			log.Println("No template list found")
			common.RespondSuccess(w, []custom_models.Product{}, http.StatusNotFound)
			return
		}
		log.Println("Error getting list by external id: ", err)
		common.RespondError(w, err)
		return
	}

	// Parse products from JSONB
	products, err := helpers.ParseJSONB[custom_models.Product](template.Products.Bytes)
	if err != nil {
		log.Println("Error parsing products: ", err)
		common.RespondError(w, fmt.Errorf("error parsing products"), http.StatusInternalServerError)
		return
	}

	response := GetProductsListResponse{
		Name:       template.Name,
		IconURL:    "https://images.izymercado.com.br/014KG56DC01GG4TEB01ZEX7WFJ.png",
		Products:   products,
		ExternalID: template.ExternalID,
		CreatedAt:  template.CreatedAt.String(),
		UpdatedAt:  template.UpdatedAt.String(),
		IsPublic:   true,
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// CreateList godoc
// @Summary Upsert a products list
// @Description Create or Update a products list
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param payload body CreateProductsListRequest true "Products list payload"
// @Success 201 {object} CreateProductsListSuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/list [post]
func (h *UserHandler) CreateList(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	payload := CreateProductsListRequest{}
	helper := helpers.New()

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	// Check if IconURL is valid
	err = helper.CheckIconURL(payload.IconURL)
	if err != nil {
		log.Println("Invalid IconURL: ", err)
		common.RespondError(w, fmt.Errorf("invalid IconURL"), 400)
		return
	}

	externalID := helper.GenerateULIDV2()
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())
	qtx := h.queries.WithTx(tx)

	list, err := qtx.UpsertProductsList(r.Context(), postgres.UpsertProductsListParams{
		UserID:     user.ID,
		Name:       payload.Name,
		IconUrl:    payload.IconURL,
		IsPublic:   *payload.IsPublic,
		ExternalID: externalID,
	},
	)
	if err != nil {
		log.Println("Error upserting products list: ", err)
		common.RespondError(w, err)
		return
	}

	productsIds := make([]string, len(payload.Products))
	// 2. Processar os produtos
	for _, product := range payload.Products {
		productsIds = append(productsIds, product.ExternalID)
		// 2.1 Upsert do item na relação de lista e produto
		err = qtx.UpsertUserProductsListItem(r.Context(), postgres.UpsertUserProductsListItemParams{
			ExternalID: product.ExternalID,
			ListID:     list.ID,
			Quantity:   int32(product.Quantity),
		})
		if err != nil {
			log.Println("Error upserting user products list item: ", err)
			common.RespondError(w, err)
			return
		}
	}

	if list.Action == "update" {
		err := qtx.DeleteMissingUserProductsListItems(r.Context(), postgres.DeleteMissingUserProductsListItemsParams{
			ListID:      list.ID,
			ExternalIds: productsIds,
		})
		if err != nil {
			log.Println("Error deleting missing products list items: ", err)
			common.RespondError(w, err)
			return
		}
	}

	if err := tx.Commit(r.Context()); err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess(w, list.ExternalID, http.StatusCreated)
}

// DeleteList godoc
// @Summary Delete user products list
// @Description Delete user products list
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param list_external_id path string true "List external id"
// @Success 200 {object} EmptySuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 401 {object} common.ErrorResponse "unauthorized"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/list/{list_external_id} [delete]
func (h *UserHandler) DeleteList(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	listExternalID := chi.URLParam(r, "list_external_id")
	err := h.queries.DeleteUserProductsList(r.Context(), postgres.DeleteUserProductsListParams{
		UserID:     user.ID,
		ExternalID: listExternalID,
	})
	if err != nil {
		log.Println("Error deleting user products list: ", err)
		common.RespondError(w, err)
		return
	}
	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// GetList godoc
// @Summary Get user products list
// @Description Get user products list
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query string false "Page"
// @Param limit query string false "Limit"
// @Success 200 {object} GetProductsListSuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/list [get]
func (h *UserHandler) GetList(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	offset := (page - 1) * limit

	lists, err := h.queries.GetUserProductsLists(r.Context(), postgres.GetUserProductsListsParams{
		UserID: user.ID,
		Limit:  int32(limit),
		Offset: int32(offset),
	})
	if err != nil {
		common.RespondError(w, err)
		return
	}

	totalItems := 0
	response := make([]GetProductsListResponse, len(lists))
	for i, list := range lists {
		if list.Products.Bytes == nil {
			list.Products.Bytes = []byte("[]")
		}

		products, err := helpers.ParseJSONB[custom_models.Product](list.Products.Bytes)
		if err != nil {
			log.Printf("Error parsing products: %v\n", err)
			common.RespondError(w, err)
			return
		}

		totalItems = int(list.TotalCount)
		response[i] = GetProductsListResponse{
			Name:       list.Name,
			IconURL:    list.IconUrl,
			Products:   products,
			IsPublic:   list.IsPublic,
			ExternalID: list.ExternalID,
			CreatedAt:  list.CreatedAt.String(),
			UpdatedAt:  list.UpdatedAt.String(),
		}
	}

	common.RespondSuccessWithPagination(w, response, page, limit, totalItems)
}

// GetOneList godoc
// @Summary Get user products list by external id
// @Description Get user products list by external id
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param list_external_id path string true "List external id"
// @Success 200 {object} GetProductsListSuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 401 {object} common.ErrorResponse "unauthorized"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/list/{list_external_id} [get]
func (h *UserHandler) GetOneList(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	listExternalID := chi.URLParam(r, "list_external_id")
	list, err := h.queries.GetUserProductsListByExternalId(r.Context(), postgres.GetUserProductsListByExternalIdParams{
		UserID:     user.ID,
		ExternalID: listExternalID,
	})
	if err != nil {
		if err.Error() == "no rows in result set" {
			log.Println("No products list found")
			common.RespondSuccess(w, []custom_models.Product{}, http.StatusNotFound)
			return
		}
		log.Println("Error getting list by external id: ", err)
		common.RespondError(w, err)
		return
	}
	// Parse products from JSONB
	products, err := helpers.ParseJSONB[custom_models.Product](list.Products.Bytes)
	if err != nil {
		log.Println("Error parsing products: ", err)
		common.RespondError(w, fmt.Errorf("error parsing products"), http.StatusInternalServerError)
		return
	}
	response := GetProductsListResponse{
		Name:       list.Name,
		IconURL:    list.IconUrl,
		Products:   products,
		IsPublic:   list.IsPublic,
		ExternalID: list.ExternalID,
		CreatedAt:  list.CreatedAt.String(),
		UpdatedAt:  list.UpdatedAt.String(),
	}
	common.RespondSuccess(w, response, http.StatusOK)
}

// GetListIcons godoc
// @Summary Get user products list icons
// @Description Get user products list icons
// @Tags Users
// @Accept json
// @Produce json
// @Success 200 {object} map[string]string "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/list/icons [get]
func (h *UserHandler) GetListIcons(w http.ResponseWriter, r *http.Request) {
	urls := helpers.New().GetIconURLs()

	if len(urls) == 0 {
		common.RespondError(w, fmt.Errorf("no icons found"))
		return
	}

	common.RespondSuccess(w, urls, http.StatusOK)
}

// GetMe godoc
// @Summary Get user info
// @Description Get user info
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} GetMeSuccessResponse "ok"
// @Failure 401 {object} common.ErrorResponse "unauthorized"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/me [get]
func (h *UserHandler) GetMe(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	userInfo, err := h.queries.GetMe(r.Context(), user.ID)
	if err != nil {
		log.Println("Error getting user info: ", err)
		if err.Error() == "no rows in result set" {
			common.RespondError(w, fmt.Errorf("user not found"), http.StatusNotFound)
			return
		}
		common.RespondError(w, err)
		return
	}

	// Parse addresses from JSONB
	addresses, err := helpers.ParseJSONB[custom_models.AddressParams](userInfo.Addresses.Bytes)
	if err != nil {
		log.Println("Error parsing addresses: ", err)
		common.RespondError(w, fmt.Errorf("error parsing addresses"), http.StatusInternalServerError)
		return
	}

	response := GetMeResponse{
		Name:          userInfo.Name,
		Email:         userInfo.Email,
		Cpf:           userInfo.Cpf,
		PhoneNumbers:  userInfo.PhoneNumbers,
		CashbackValue: userInfo.CashbackValue,
		ExternalID:    userInfo.ExternalID,
		Addresses:     addresses,
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// AddAddress godoc
// @Summary Upsert user address
// @Description Create or Update user address
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param payload body custom_models.AddressParams true "User address payload"
// @Success 201 {object} CreateUserAddressSuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 401 {object} common.ErrorResponse "unauthorized"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/address [post]
func (h *UserHandler) AddAddress(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	payload := custom_models.AddressParams{}
	helper := helpers.New()

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())
	qtx := h.queries.WithTx(tx)

	externalID := payload.ExternalID
	isValid, err := helper.IsULIDv2(externalID)
	if err != nil || !isValid {
		log.Println("Invalid or unparsable external ID, generating new ULIDv2:", err)
		externalID = helper.GenerateULIDV2()
	}

	if payload.IsDefault {
		err := qtx.ResetUserAddressDefault(r.Context(), user.ID)
		if err != nil {
			log.Println("Error resetting user address default: ", err)
			common.RespondError(w, err)
			return
		}
	}

	addressExternalID, err := qtx.UpsertUserAddressByDetails(r.Context(), postgres.UpsertUserAddressByDetailsParams{
		UserID:       user.ID,
		Name:         payload.Name,
		Street:       payload.Street,
		Number:       payload.Number,
		Neighborhood: payload.Neighborhood,
		ZipCode:      payload.ZipCode,
		City:         payload.City,
		Complement:   sql.NullString{String: payload.Complement, Valid: payload.Complement != ""},
		State:        payload.State,
		Latitude: sql.NullFloat64{
			Float64: payload.Location.Latitude,
			Valid:   payload.Location.Latitude != 0,
		},
		Longitude: sql.NullFloat64{
			Float64: payload.Location.Longitude,
			Valid:   payload.Location.Longitude != 0,
		},
		ExternalID: externalID,
		IsDefault:  payload.IsDefault,
	})
	if err != nil {
		log.Println("Error upserting user address: ", err)
		common.RespondError(w, err)
		return
	}

	if err := tx.Commit(r.Context()); err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}

	response := CreateUserAddressResponse{
		ExternalID: addressExternalID,
	}

	common.RespondSuccess(w, response, http.StatusCreated)
}

// DeleteAddress godoc
// @Summary Delete user address
// @Description Delete user address
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param address_external_id path string true "Address external id"
// @Success 200 {object} EmptySuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 401 {object} common.ErrorResponse "unauthorized"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/address/{address_external_id} [delete]
func (h *UserHandler) DeleteAddress(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)
	addressExternalID := chi.URLParam(r, "address_external_id")

	err := h.queries.DeleteUserAddress(r.Context(), postgres.DeleteUserAddressParams{
		UserID:     user.ID,
		ExternalID: addressExternalID,
	})
	if err != nil {
		log.Println("Error deleting user address: ", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// UpdateName godoc
// @Summary Update name
// @Description Update name
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param payload body UpdateUserNameRequest true "User name payload"
// @Success 200 {object} EmptySuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 401 {object} common.ErrorResponse "unauthorized"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/user/name [patch]
func (h *UserHandler) UpdateName(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	payload := UpdateUserNameRequest{}
	helper := helpers.New()

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	err = h.queries.UpdateUserName(r.Context(), postgres.UpdateUserNameParams{
		ID:   user.ID,
		Name: payload.Name,
	})
	if err != nil {
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// GetInvoices godoc
// @Summary Get user invoices
// @Description Get all invoices for the authenticated user with company information and products
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} GetInvoicesSuccessResponse "User invoices retrieved successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request - invalid pagination parameters"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/user/invoice [get]
func (h *UserHandler) GetInvoices(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context (set by authentication middleware)
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		log.Println("User not found in context")
		common.RespondError(w, fmt.Errorf("user not authenticated"), http.StatusUnauthorized)
		return
	}

	// Parse pagination parameters
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	offset := (page - 1) * limit

	// Get user invoices with pagination
	invoices, err := h.queries.GetUserInvoices(ctx, postgres.GetUserInvoicesParams{
		UserID: user.ID,
		Limit:  int32(limit),
		Offset: int32(offset),
	})
	if err != nil {
		log.Printf("Failed to get user invoices for user %d: %v\n", user.ID, err)
		common.RespondError(w, err)
		return
	}

	// Convert to response format
	invoiceResponses := make([]InvoiceResponse, len(invoices))
	totalItems := 0
	for i, invoice := range invoices {
		totalItems = int(invoice.TotalCount)
		var products []InvoiceProductResponse
		if invoice.Products != nil {
			// Convert interface{} to JSON bytes first, then use ParseJSONB
			productsBytes, err := json.Marshal(invoice.Products)
			if err != nil {
				log.Printf("Failed to marshal invoice products for invoice %d: %v\n", invoice.ID, err)
				products = []InvoiceProductResponse{}
			} else {
				// Use ParseJSONB helper to parse the products
				rawProducts, err := helpers.ParseJSONB[map[string]interface{}](productsBytes)
				if err != nil {
					log.Printf("Failed to parse invoice products for invoice %d: %v\n", invoice.ID, err)
					products = []InvoiceProductResponse{}
				} else {
					products = make([]InvoiceProductResponse, len(rawProducts))
					for j, p := range rawProducts {
						// Parse product categories
						var categories []custom_models.Category
						if categoriesData, exists := p["product_categories"]; exists && categoriesData != nil {
							// Convert to JSON bytes and parse
							categoriesBytes, err := json.Marshal(categoriesData)
							if err != nil {
								log.Printf("Failed to marshal product categories for product %s: %v\n", p["product_external_id"], err)
								categories = []custom_models.Category{}
							} else {
								err := json.Unmarshal(categoriesBytes, &categories)
								if err != nil {
									log.Printf("Failed to parse product categories for product %s: %v\n", p["product_external_id"], err)
									categories = []custom_models.Category{}
								}
							}
						} else {
							categories = []custom_models.Category{}
						}

						products[j] = InvoiceProductResponse{
							Quantity:   int32(p["quantity"].(float64)),
							Price:      int32(p["unit_price"].(float64)),
							Discount:   int32(p["discount"].(float64)),
							Name:       p["product_name"].(string),
							Ean:        p["product_ean"].(string),
							ExternalID: p["product_external_id"].(string),
							Brand:      helpers.NullStringToPtr(p["product_brand"]),
							Image:      helpers.NullStringToPtr(p["product_image"]),
							Categories: categories,
						}
					}
				}
			}
		} else {
			products = []InvoiceProductResponse{}
		}

		// Parse company address from JSONB
		var companyAddress custom_models.AddressParams
		if invoice.CompanyAddress.Bytes != nil {
			// First unmarshal to a single object since the query now returns a single object
			err := json.Unmarshal(invoice.CompanyAddress.Bytes, &companyAddress)
			if err != nil {
				log.Printf("Failed to parse company address for invoice %d: %v\n", invoice.ID, err)
				// Use default empty address
				companyAddress = custom_models.AddressParams{}
			}
		} else {
			// Use default empty address
			companyAddress = custom_models.AddressParams{}
		}

		// Parse payment data from info_details
		var brCode, qrCodeImage *string
		if invoice.InfoDetails.Status == pgtype.Present {
			paymentData, err := helpers.ParsePaymentData(invoice.InfoDetails.Bytes)
			if err != nil {
				log.Printf("Error parsing payment data for invoice %s: %v\n", invoice.OrderID, err)
			} else if paymentData != nil {
				if paymentData.BrCode != "" {
					brCode = &paymentData.BrCode
				}
				if paymentData.QrCodeImage != "" {
					qrCodeImage = &paymentData.QrCodeImage
				}
			}
		}

		invoiceResponses[i] = InvoiceResponse{
			OrderID:             invoice.OrderID,
			Status:              invoice.Status,
			StatusDescription:   helpers.GetStatusDescription(invoice.Status),
			PaymentMethod:       invoice.PaymentMethod,
			Amount:              invoice.Amount,
			Discount:            invoice.Discount,
			ShippingFee:         invoice.ShippingFee,
			CompanyAmount:       invoice.CompanyAmount,
			DeliveryMode:        invoice.DeliveryMode,
			Coupon:              invoice.Coupon.String,
			UserAddress:         invoice.UserAddress.String,
			UserPhoneNumber:     invoice.UserPhoneNumber.String,
			CompanyName:         invoice.CompanyName.String,
			CompanyCnpj:         invoice.CompanyCnpj.String,
			CompanyAddress:      companyAddress,
			CompanyBio:          invoice.CompanyBio.String,
			CompanyPicture:      invoice.CompanyPicture.String,
			CompanyPhoneNumbers: invoice.CompanyPhoneNumbers,
			CompanyPixKey:       invoice.CompanyPixKey.String,
			CompanyExternalID:   invoice.CompanyExternalID.String,
			Info:                invoice.Info.String,
			BrCode:              brCode,
			QrCodeImage:         qrCodeImage,
			FinishedAt: func() *time.Time {
				if invoice.FinishedAt.Valid {
					return &invoice.FinishedAt.Time
				} else {
					return nil
				}
			}(),
			CreatedAt: invoice.CreatedAt,
			UpdatedAt: invoice.UpdatedAt,
			Products:  products,
		}
	}

	log.Printf("Successfully retrieved %d invoices for user %d (page %d, limit %d)\n", len(invoices), user.ID, page, limit)

	common.RespondSuccessWithPagination(w, invoiceResponses, page, limit, totalItems)
}

// GetInvoiceByOrderID godoc
// @Summary Get user invoice by order ID
// @Description Get a specific invoice for the authenticated user by order ID with complete information including company details and products
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param order_id path string true "Order ID"
// @Success 200 {object} GetInvoiceByOrderIDSuccessResponse "User invoice retrieved successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request - missing order_id parameter"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - user doesn't own this invoice"
// @Failure 404 {object} common.ErrorResponse "Invoice not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/user/invoice/{order_id} [get]
func (h *UserHandler) GetInvoiceByOrderID(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context (set by authentication middleware)
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		log.Println("User not found in context")
		common.RespondError(w, fmt.Errorf("user not authenticated"), http.StatusUnauthorized)
		return
	}

	// Get order_id from URL
	orderID := chi.URLParam(r, "order_id")
	if orderID == "" {
		common.RespondError(w, fmt.Errorf("missing order_id parameter"), http.StatusBadRequest)
		return
	}

	// Get user invoice by order ID with security check
	invoice, err := h.queries.GetUserInvoiceByOrderID(ctx, postgres.GetUserInvoiceByOrderIDParams{
		OrderID: orderID,
		UserID:  user.ID,
	})
	if err != nil {
		if err.Error() == "no rows in result set" {
			log.Printf("Invoice not found for order_id %s and user_id %d\n", orderID, user.ID)
			common.RespondError(w, fmt.Errorf("invoice not found"), http.StatusNotFound)
			return
		}
		log.Printf("Failed to get user invoice for order_id %s and user_id %d: %v\n", orderID, user.ID, err)
		common.RespondError(w, err)
		return
	}

	// Parse company address from JSONB (same logic as GetInvoices)
	var companyAddress custom_models.AddressParams
	if invoice.CompanyAddress.Bytes != nil {
		// First unmarshal to a single object since the query now returns a single object
		err := json.Unmarshal(invoice.CompanyAddress.Bytes, &companyAddress)
		if err != nil {
			log.Printf("Failed to parse company address for invoice %s: %v\n", orderID, err)
			// Use default empty address
			companyAddress = custom_models.AddressParams{}
		}
	} else {
		// Use default empty address
		companyAddress = custom_models.AddressParams{}
	}

	// Parse products from JSON (same logic as GetInvoices)
	var products []InvoiceProductResponse
	if invoice.Products != nil {
		// Convert interface{} to JSON bytes first, then use ParseJSONB
		productsBytes, err := json.Marshal(invoice.Products)
		if err != nil {
			log.Printf("Failed to marshal invoice products for invoice %s: %v\n", orderID, err)
			products = []InvoiceProductResponse{}
		} else {
			// Use ParseJSONB helper to parse the products (exact same pattern as GetInvoices)
			rawProducts, err := helpers.ParseJSONB[map[string]interface{}](productsBytes)
			if err != nil {
				log.Printf("Failed to parse invoice products for invoice %s: %v\n", orderID, err)
				products = []InvoiceProductResponse{}
			} else {
				products = make([]InvoiceProductResponse, len(rawProducts))
				for j, p := range rawProducts {
					// Parse product categories
					var categories []custom_models.Category
					if categoriesData, exists := p["product_categories"]; exists && categoriesData != nil {
						// Convert to JSON bytes and parse
						categoriesBytes, err := json.Marshal(categoriesData)
						if err != nil {
							log.Printf("Failed to marshal product categories for product %s: %v\n", p["product_external_id"], err)
							categories = []custom_models.Category{}
						} else {
							err := json.Unmarshal(categoriesBytes, &categories)
							if err != nil {
								log.Printf("Failed to parse product categories for product %s: %v\n", p["product_external_id"], err)
								categories = []custom_models.Category{}
							}
						}
					} else {
						categories = []custom_models.Category{}
					}

					products[j] = InvoiceProductResponse{
						Quantity:   int32(p["quantity"].(float64)),
						Price:      int32(p["unit_price"].(float64)),
						Discount:   int32(p["discount"].(float64)),
						Name:       p["product_name"].(string),
						Ean:        p["product_ean"].(string),
						ExternalID: p["product_external_id"].(string),
						Brand:      helpers.NullStringToPtr(p["product_brand"]),
						Image:      helpers.NullStringToPtr(p["product_image"]),
						Categories: categories,
					}
				}
			}
		}
	} else {
		products = []InvoiceProductResponse{}
	}

	// Parse payment data from info_details (same logic as GetInvoices)
	var brCode, qrCodeImage *string
	if invoice.InfoDetails.Status == pgtype.Present {
		paymentData, err := helpers.ParsePaymentData(invoice.InfoDetails.Bytes)
		if err != nil {
			log.Printf("Error parsing payment data for invoice %s: %v\n", orderID, err)
		} else if paymentData != nil {
			if paymentData.BrCode != "" {
				brCode = &paymentData.BrCode
			}
			if paymentData.QrCodeImage != "" {
				qrCodeImage = &paymentData.QrCodeImage
			}
		}
	}

	// Build response (same structure as GetInvoices)
	response := InvoiceResponse{
		OrderID:             invoice.OrderID,
		Status:              invoice.Status,
		StatusDescription:   helpers.GetStatusDescription(invoice.Status),
		PaymentMethod:       invoice.PaymentMethod,
		Amount:              invoice.Amount,
		Discount:            invoice.Discount,
		ShippingFee:         invoice.ShippingFee,
		CompanyAmount:       invoice.CompanyAmount,
		DeliveryMode:        invoice.DeliveryMode,
		Coupon:              invoice.Coupon.String,
		UserAddress:         invoice.UserAddress.String,
		UserPhoneNumber:     invoice.UserPhoneNumber.String,
		CompanyName:         invoice.CompanyName.String,
		CompanyCnpj:         invoice.CompanyCnpj.String,
		CompanyAddress:      companyAddress,
		CompanyBio:          invoice.CompanyBio.String,
		CompanyPicture:      invoice.CompanyPicture.String,
		CompanyPhoneNumbers: invoice.CompanyPhoneNumbers,
		CompanyPixKey:       invoice.CompanyPixKey.String,
		CompanyExternalID:   invoice.CompanyExternalID.String,
		Info:                invoice.Info.String,
		BrCode:              brCode,
		QrCodeImage:         qrCodeImage,
		FinishedAt: func() *time.Time {
			if invoice.FinishedAt.Valid {
				return &invoice.FinishedAt.Time
			} else {
				return nil
			}
		}(),
		CreatedAt: invoice.CreatedAt,
		UpdatedAt: invoice.UpdatedAt,
		Products:  products,
	}

	log.Printf("Successfully retrieved invoice %s for user %d\n", orderID, user.ID)

	common.RespondSuccess(w, response, http.StatusOK)
}

// UpdateInvoiceStatus godoc
// @Summary Update user invoice status
// @Description Update the status of a user's invoice (allows cancellation in specific states)
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param order_id path string true "Order ID"
// @Param payload body UpdateStatusRequest true "Status update payload"
// @Success 200 {object} UpdateInvoiceStatusSuccessResponse "Invoice status updated successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request - invalid status or transition"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - user doesn't own invoice or invalid cancellation"
// @Failure 404 {object} common.ErrorResponse "Invoice not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/user/invoice/{order_id}/status [put]
func (h *UserHandler) UpdateInvoiceStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		log.Println("User not found in context")
		common.RespondError(w, fmt.Errorf("user not authenticated"), http.StatusUnauthorized)
		return
	}

	// Get order_id from URL
	orderID := chi.URLParam(r, "order_id")
	if orderID == "" {
		common.RespondError(w, fmt.Errorf("missing order_id parameter"), http.StatusBadRequest)
		return
	}

	// Parse request body
	var req UpdateStatusRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		log.Printf("Failed to decode request body: %v\n", err)
		common.RespondError(w, fmt.Errorf("invalid request body"), http.StatusBadRequest)
		return
	}

	// Get current invoice
	invoice, err := h.queries.GetInvoiceByOrderID(ctx, orderID)
	if err != nil {
		log.Printf("Failed to get invoice with order_id %s: %v\n", orderID, err)
		common.RespondError(w, fmt.Errorf("invoice not found"), http.StatusNotFound)
		return
	}

	// Verify user owns this invoice
	if invoice.UserID != user.ID {
		log.Printf("User %d attempted to update invoice %d they don't own\n", user.ID, invoice.ID)
		common.RespondError(w, fmt.Errorf("you don't have permission to update this invoice"), http.StatusForbidden)
		return
	}

	// Validate status update
	if err := helpers.ValidateStatusUpdate(invoice.Status, req.Status, true, false); err != nil {
		log.Printf("Invalid status update attempt from %s to %s for order %s: %v\n", invoice.Status, req.Status, orderID, err)
		common.RespondError(w, err, http.StatusBadRequest)
		return
	}

	// Handle pending Woovi charges when cancelling from mobile app
	if req.Status == "cancelled" && invoice.PaymentMethod == "pix" {
		log.Printf("Invoice cancellation detected for order %s, handling pending charge\n", orderID)

		// Use orderID directly for Woovi API calls
		err := h.woovi.HandlePendingChargeOnCancellation(ctx, orderID)
		if err != nil {
			log.Printf("Warning: Failed to handle pending charge for order %s: %v\n", orderID, err)
			// Note: We don't fail the cancellation if charge handling fails
			// The invoice cancellation should proceed regardless
		} else {
			log.Printf("Successfully handled pending charge for order %s\n", orderID)
		}
	}

	// Update invoice status
	updatedInvoice, err := h.queries.UpdateInvoiceStatusByOrderID(ctx, postgres.UpdateInvoiceStatusByOrderIDParams{
		OrderID:    orderID,
		Status:     req.Status,
		Info:       sql.NullString{}, // Users don't provide reasons, so always empty
		FinishedAt: sql.NullTime{},   // Users don't set finished_at, so always empty
	})
	if err != nil {
		log.Printf("Failed to update invoice status for order %s: %v\n", orderID, err)
		common.RespondError(w, err)
		return
	}

	log.Printf("User %d updated invoice %d status from %s to %s (order: %s)\n", user.ID, updatedInvoice.ID, invoice.Status, req.Status, orderID)

	// Get company information for the notification
	company, err := h.queries.GetCompanyByID(ctx, invoice.CompanyID)
	if err != nil {
		log.Printf("Error getting company info for notification: %v", err)
		company.Name = "Unknown Company"
	}

	// Send FCM notification
	if h.fcmService != nil {
		fcmPayload := fcm.NotificationPayload{
			Type:              "invoice_status_update",
			OrderID:           orderID,
			NewStatus:         req.Status,
			OldStatus:         invoice.Status,
			StatusDescription: helpers.GetStatusDescription(req.Status),
			Message:           fmt.Sprintf("Status do pedido atualizado para %s pelo usuário", helpers.GetStatusDescription(req.Status)),
			UpdatedBy:         "user",
			UpdaterName:       user.Name,
			UserExternalID:    user.ExternalID,
			CompanyExternalID: company.ExternalID,
			CompanyName:       company.Name,
			Timestamp:         time.Now(),
		}
		result, err := h.fcmService.SendInvoiceStatusNotification(ctx, fcmPayload)
		if err != nil {
			log.Printf("Error sending FCM notification for user invoice status update: %v", err)
		} else if result.Success {
			log.Printf("FCM notification sent for user invoice status update: OrderID=%s, UserExternalID=%s, MessageID=%s",
				orderID, user.ExternalID, result.MessageID)
		} else {
			log.Printf("FCM notification failed for user invoice status update: OrderID=%s, Error=%s",
				orderID, result.Error)
		}
	}

	response := UpdateInvoiceStatusResponse{
		Message:           "Invoice status updated successfully",
		OrderID:           orderID,
		Status:            req.Status,
		StatusDescription: helpers.GetStatusDescription(req.Status),
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// AssignRoleToUser godoc
// @Summary Assign role to user
// @Description Assign a role to a user
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param payload body AssignRoleToUserRequest true "Assign role payload"
// @Success 200 {object} AssignRoleToUserSuccessResponse "Role assigned successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/user/role [post]
func (h *UserHandler) AssignRoleToUser(w http.ResponseWriter, r *http.Request) {
	payload := AssignRoleToUserRequest{}
	helper := helpers.New()

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	user, err := h.queries.GetUserByExternalID(r.Context(), payload.ExternalID)
	if err != nil {
		log.Println("Error getting user by external ID: ", err)
		if err.Error() == "no rows in result set" {
			common.RespondError(w, fmt.Errorf("user not found"), http.StatusNotFound)
			return
		}
		common.RespondError(w, err)
		return
	}

	err = h.queries.AssignRoleToUser(r.Context(), postgres.AssignRoleToUserParams{
		UserID: user.ID,
		Name:   payload.Role,
	})
	if err != nil {
		log.Println("Error assigning role to user: ", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusCreated)
}

// SearchUser godoc
// @Summary Search users
// @Description Search users by CPF, name, or email using case-insensitive partial matching
// @Tags Users
// @Accept json
// @Produce json
// @Security Bearer
// @Param query path string true "Search query (minimum 2 characters)" minlength(2)
// @Param page query int false "Page number (default: 1)" minimum(1)
// @Param limit query int false "Items per page (default: 10, max: 100)" minimum(1) maximum(100)
// @Success 200 {object} SearchUsersSuccessResponse "Users found"
// @Failure 400 {object} common.ErrorResponse "Bad request - invalid query or pagination parameters"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - admin role required"
// @Failure 404 {object} common.ErrorResponse "No users found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/user/search/{query} [get]
func (h *UserHandler) SearchUser(w http.ResponseWriter, r *http.Request) {
	// Get query parameter from URL path
	query := chi.URLParam(r, "query")
	if query == "" {
		common.RespondError(w, fmt.Errorf("search query is required"), http.StatusBadRequest)
		return
	}

	// Validate query length (minimum 2 characters to prevent overly broad searches)
	if len(query) < 2 {
		common.RespondError(w, fmt.Errorf("search query must be at least 2 characters long"), http.StatusBadRequest)
		return
	}

	// Sanitize query to prevent SQL injection (basic validation)
	helper := helpers.New()
	if err := helper.ValidateSearchQuery(query); err != nil {
		common.RespondError(w, fmt.Errorf("invalid search query: %v", err), http.StatusBadRequest)
		return
	}

	// Parse pagination parameters
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page parameter"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 || l > 100 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit parameter (must be between 1 and 100)"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	offset := (page - 1) * limit

	// Search users in database
	users, err := h.queries.SearchUsers(r.Context(), postgres.SearchUsersParams{
		SearchQuery: query,
		Limit:       int32(limit),
		Offset:      int32(offset),
	})
	if err != nil {
		log.Printf("Error searching users with query '%s': %v\n", query, err)
		common.RespondError(w, err)
		return
	}

	// Check if any users were found
	if len(users) == 0 {
		log.Printf("No users found for search query: %s\n", query)
		common.RespondError(w, fmt.Errorf("no users found matching the search criteria"), http.StatusNotFound)
		return
	}

	// Convert to response format
	userResponses := make([]SearchUsersResponse, len(users))
	totalItems := 0
	for i, user := range users {
		totalItems = int(user.TotalCount)

		// Handle nullable subscription_id
		var subscriptionID *int32
		if user.SubscriptionID.Valid {
			subscriptionID = &user.SubscriptionID.Int32
		}

		userResponses[i] = SearchUsersResponse{
			ExternalID:     user.ExternalID,
			Name:           user.Name,
			Email:          user.Email,
			Cpf:            user.Cpf,
			PhoneNumbers:   user.PhoneNumbers,
			CashbackValue:  user.CashbackValue,
			SubscriptionID: subscriptionID,
			IsActive:       user.IsActive,
			IsDeleted:      user.IsDeleted,
			CreatedAt:      user.CreatedAt.Format(time.RFC3339),
			UpdatedAt:      user.UpdatedAt.Format(time.RFC3339),
		}
	}

	log.Printf("Successfully found %d users for search query '%s' (page %d, limit %d)\n", len(users), query, page, limit)

	common.RespondSuccessWithPagination(w, userResponses, page, limit, totalItems)
}

// UpdateUserStatus godoc
// @Summary Update user status (activate/deactivate)
// @Description Update user status by setting is_active to true or false. Admin access required.
// @Tags Users
// @Security Bearer
// @Accept json
// @Produce json
// @Param external_id path string true "User External ID"
// @Param payload body UpdateUserStatusRequest true "User status data"
// @Success 200 {object} UpdateUserStatusSuccessResponse "User status updated successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request - invalid external_id or payload"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - admin role required"
// @Failure 404 {object} common.ErrorResponse "User not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/user/{external_id}/status [patch]
func (h *UserHandler) UpdateUserStatus(w http.ResponseWriter, r *http.Request) {
	externalID := chi.URLParam(r, "external_id")
	if externalID == "" {
		log.Println("External ID is empty")
		common.RespondError(w, fmt.Errorf("external_id is required"), http.StatusBadRequest)
		return
	}

	// Validate external_id format (ULID should be 26 characters)
	if len(externalID) != 26 {
		log.Printf("Invalid external_id format: %s (length: %d)\n", externalID, len(externalID))
		common.RespondError(w, fmt.Errorf("invalid external_id format"), http.StatusBadRequest)
		return
	}

	// Parse request body
	payload := UpdateUserStatusRequest{}
	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Printf("Failed to decode request body: %v", err)
		common.RespondError(w, fmt.Errorf("invalid request body"), http.StatusBadRequest)
		return
	}

	helper := helpers.New()
	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), http.StatusBadRequest)
		return
	}

	// Verify user exists before updating
	_, err = h.queries.GetUserByExternalID(r.Context(), externalID)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("User not found: %s\n", externalID)
			common.RespondError(w, fmt.Errorf("user not found"), http.StatusNotFound)
			return
		}
		log.Printf("Error getting user %s: %v\n", externalID, err)
		common.RespondError(w, err)
		return
	}

	// Update user status
	updatedUser, err := h.queries.UpdateUserStatus(r.Context(), postgres.UpdateUserStatusParams{
		ExternalID: externalID,
		IsActive:   *payload.IsActive,
	})
	if err != nil {
		log.Printf("Failed to update user status for %s: %v\n", externalID, err)
		common.RespondError(w, err)
		return
	}

	log.Printf("Successfully updated user %s status to %t\n", externalID, *payload.IsActive)

	response := UpdateUserStatusResponse{
		ExternalID: updatedUser.ExternalID,
		Name:       updatedUser.Name,
		IsActive:   updatedUser.IsActive,
	}

	common.RespondSuccess(w, response, http.StatusOK)
}
